<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/FBAdBridgeCommon.h</key>
		<data>
		BtpCU0SEsTaWwPXhZG/xryJ14rA=
		</data>
		<key>Headers/FBAdBridgeContainer.h</key>
		<data>
		VCjtFROZ6vA+hgirBnmNEZG/05U=
		</data>
		<key>Headers/FBAdChoicesView.h</key>
		<data>
		mPVLE+AFJ3hHwfxTaXS82xOGDqM=
		</data>
		<key>Headers/FBAdCompanionView.h</key>
		<data>
		nPor4UpY2+3HUmWaWaOcMYRhfJs=
		</data>
		<key>Headers/FBAdDefines.h</key>
		<data>
		eF5YA2yTwZ/qymrHCLrHNXxCcks=
		</data>
		<key>Headers/FBAdExperienceConfig.h</key>
		<data>
		IWXtxQr66bLlIaz0MZEx+GTWdmg=
		</data>
		<key>Headers/FBAdExtraHint.h</key>
		<data>
		n7hvtmm7C4VcB1D1XRUlxhw2ol4=
		</data>
		<key>Headers/FBAdIconView.h</key>
		<data>
		mYVzuZvmyQNMvmvzaAYi0RbESdA=
		</data>
		<key>Headers/FBAdImage.h</key>
		<data>
		OaVQV9ZKvcm0C1JBmeTqdWCw7mA=
		</data>
		<key>Headers/FBAdOptionsView.h</key>
		<data>
		Id+HrpoYLEEmgxgEUQhvGfap6bU=
		</data>
		<key>Headers/FBAdSDKNotificationManager.h</key>
		<data>
		Kr1b5BaDE9+Niq1J7t1P+MUGSZI=
		</data>
		<key>Headers/FBAdSettings.h</key>
		<data>
		934rUlgBtzwvwfBeyMMN1YPbgfI=
		</data>
		<key>Headers/FBAdSettingsBridge.h</key>
		<data>
		otXXaNl3ivITTXmQnbVsH+2bqoU=
		</data>
		<key>Headers/FBAdSize.h</key>
		<data>
		467OPNHvQN+SuTpujVQIu6UHKS8=
		</data>
		<key>Headers/FBAdUtilityBridge.h</key>
		<data>
		gAsta3Ku0CWuWl5UTPHKiH0Htak=
		</data>
		<key>Headers/FBAdView.h</key>
		<data>
		63WRrf+IX626fzGNbMmHZv24gNA=
		</data>
		<key>Headers/FBAdViewBridge.h</key>
		<data>
		OodJ7CV+yZ7t4qMfaw+Kf28t8b8=
		</data>
		<key>Headers/FBAudienceNetwork.h</key>
		<data>
		Koz9Fi0nOjJ9WWjoAb1uqzgktNE=
		</data>
		<key>Headers/FBAudienceNetworkAds.h</key>
		<data>
		H/X7/UR12iWRVjaSltqIiArIosM=
		</data>
		<key>Headers/FBDynamicBannerAd.h</key>
		<data>
		tsiK6PU6SwlcUUO3nvmecWvIq0A=
		</data>
		<key>Headers/FBInterstitialAd.h</key>
		<data>
		lIlaP5u77Wek+3RM2x88LAnpVVk=
		</data>
		<key>Headers/FBInterstitialAdBridge.h</key>
		<data>
		q0sSvvj1o8+x5LmKwxlhIV/565k=
		</data>
		<key>Headers/FBMediaView.h</key>
		<data>
		XcW9MWOORzas0upnWUPgaLToIaY=
		</data>
		<key>Headers/FBMediaViewVideoRenderer.h</key>
		<data>
		jD5UU9ICd4La/BLkWy6cDpjBwQw=
		</data>
		<key>Headers/FBNativeAd.h</key>
		<data>
		3ja15NIMLK6OWYjdfRKs0kthxi0=
		</data>
		<key>Headers/FBNativeAdBase.h</key>
		<data>
		l+lfH5qJMxJy03BevlHJnaMbrhQ=
		</data>
		<key>Headers/FBNativeAdBaseView.h</key>
		<data>
		rewXycBU2KSWl8gauwuiGTRq6co=
		</data>
		<key>Headers/FBNativeAdCollectionViewAdProvider.h</key>
		<data>
		/zUpuGlyxJuQVIdk34SYBqg1da8=
		</data>
		<key>Headers/FBNativeAdCollectionViewCellProvider.h</key>
		<data>
		5W/OoYUbOOd5XUWJIN2EPFC+5Wg=
		</data>
		<key>Headers/FBNativeAdScrollView.h</key>
		<data>
		AIJ+BPRGadRoFOcJq4byzLCNNVI=
		</data>
		<key>Headers/FBNativeAdTableViewAdProvider.h</key>
		<data>
		cTgHcaEBZJiOOHqrNJgFYSOTFxc=
		</data>
		<key>Headers/FBNativeAdTableViewCellProvider.h</key>
		<data>
		p+bqYNQZS8SWtgZheKS83Hovokg=
		</data>
		<key>Headers/FBNativeAdView.h</key>
		<data>
		J9FN9bQtZnm2TSMxR6vdypm5OtM=
		</data>
		<key>Headers/FBNativeAdViewAttributes.h</key>
		<data>
		+02NIOY5GtfKVtV9WR6ELAKv6gU=
		</data>
		<key>Headers/FBNativeAdsManager.h</key>
		<data>
		SER5cKJ0SHgxJ0VGBKCE9D5pFbQ=
		</data>
		<key>Headers/FBNativeBannerAd.h</key>
		<data>
		M0ynY/kAU4FSXp0YzQUwPLR8AAE=
		</data>
		<key>Headers/FBNativeBannerAdView.h</key>
		<data>
		hPi9YaW20M8SibWRzcHsvxgB7aI=
		</data>
		<key>Headers/FBRewardedInterstitialAd.h</key>
		<data>
		Ja3i8kgcHuIyVokVSFqkrNJJaIc=
		</data>
		<key>Headers/FBRewardedVideoAd.h</key>
		<data>
		HTglpeO2IlcK2Sy2WyZsyyu/w+s=
		</data>
		<key>Headers/FBRewardedVideoAdBridge.h</key>
		<data>
		1bMVuSeL7cq06A9kiGQjLeMvy5s=
		</data>
		<key>Headers/UIView+FBNativeAdViewTag.h</key>
		<data>
		UIyv4MmxUtXcBhhp1hyYKHadxTA=
		</data>
		<key>Info.plist</key>
		<data>
		wKdTzmnW6sqCoBYsT65FPonYqWg=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		eL2I/YyQvaoNWry0nbEJDR6NAhQ=
		</data>
		<key>PkgInfo</key>
		<data>
		cBYDa3QyibPnSPDvyuw5uDs0LWg=
		</data>
		<key>PrivacyInfo.xcprivacy</key>
		<data>
		iJf6SpfSfy71MDcGI+K9k1a78DU=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/FBAdBridgeCommon.h</key>
		<dict>
			<key>hash</key>
			<data>
			BtpCU0SEsTaWwPXhZG/xryJ14rA=
			</data>
			<key>hash2</key>
			<data>
			amaDkW5IUAigoQucSvZdqggJ9bJFNojLdc4Kl37mE1E=
			</data>
		</dict>
		<key>Headers/FBAdBridgeContainer.h</key>
		<dict>
			<key>hash</key>
			<data>
			VCjtFROZ6vA+hgirBnmNEZG/05U=
			</data>
			<key>hash2</key>
			<data>
			7pgAucyPVPz8a4OeQjcjBPFALFNjtGohai8Pi4gCSBM=
			</data>
		</dict>
		<key>Headers/FBAdChoicesView.h</key>
		<dict>
			<key>hash</key>
			<data>
			mPVLE+AFJ3hHwfxTaXS82xOGDqM=
			</data>
			<key>hash2</key>
			<data>
			m8MZnnysUAasNl5+2cwlO03dNvOVI/x5CmRnWP/rDEc=
			</data>
		</dict>
		<key>Headers/FBAdCompanionView.h</key>
		<dict>
			<key>hash</key>
			<data>
			nPor4UpY2+3HUmWaWaOcMYRhfJs=
			</data>
			<key>hash2</key>
			<data>
			8+MrQaycGt04NRbtNxtiwHUsctGnmJA2VRB4+7LZgYQ=
			</data>
		</dict>
		<key>Headers/FBAdDefines.h</key>
		<dict>
			<key>hash</key>
			<data>
			eF5YA2yTwZ/qymrHCLrHNXxCcks=
			</data>
			<key>hash2</key>
			<data>
			3CWJcYm2XCY646OOi0BxVi+RoONw3thuDzAEq0MLJ1Y=
			</data>
		</dict>
		<key>Headers/FBAdExperienceConfig.h</key>
		<dict>
			<key>hash</key>
			<data>
			IWXtxQr66bLlIaz0MZEx+GTWdmg=
			</data>
			<key>hash2</key>
			<data>
			lPW2X42jJ+W9PGhaZaXOH/nyAJfv0maGZl/zrf7N5Hs=
			</data>
		</dict>
		<key>Headers/FBAdExtraHint.h</key>
		<dict>
			<key>hash</key>
			<data>
			n7hvtmm7C4VcB1D1XRUlxhw2ol4=
			</data>
			<key>hash2</key>
			<data>
			T1yeJNtdmHpZqJFqQiJ69JITwAIt1MLJ0TJlKRuinW4=
			</data>
		</dict>
		<key>Headers/FBAdIconView.h</key>
		<dict>
			<key>hash</key>
			<data>
			mYVzuZvmyQNMvmvzaAYi0RbESdA=
			</data>
			<key>hash2</key>
			<data>
			JibHgKTOs6wDtAzaPgZvkGM3To4sowPwMfdYYtimfLM=
			</data>
		</dict>
		<key>Headers/FBAdImage.h</key>
		<dict>
			<key>hash</key>
			<data>
			OaVQV9ZKvcm0C1JBmeTqdWCw7mA=
			</data>
			<key>hash2</key>
			<data>
			9BvTeQ5iGpHNNil5K/TmAhTchYnsaVDRhclDTgoB7Zg=
			</data>
		</dict>
		<key>Headers/FBAdOptionsView.h</key>
		<dict>
			<key>hash</key>
			<data>
			Id+HrpoYLEEmgxgEUQhvGfap6bU=
			</data>
			<key>hash2</key>
			<data>
			AyTv7l1bIkP5CvBa+LUKGvktSdUX6xevDJRsJuinLd0=
			</data>
		</dict>
		<key>Headers/FBAdSDKNotificationManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			Kr1b5BaDE9+Niq1J7t1P+MUGSZI=
			</data>
			<key>hash2</key>
			<data>
			uvVwsWym2LtMl+eGO9BYk7zDvMRit4iaew7jlAApIr8=
			</data>
		</dict>
		<key>Headers/FBAdSettings.h</key>
		<dict>
			<key>hash</key>
			<data>
			934rUlgBtzwvwfBeyMMN1YPbgfI=
			</data>
			<key>hash2</key>
			<data>
			an02TE/wVcCFLtK5iIN6vG+lJC2I8lFZ2NbMT/qZBxg=
			</data>
		</dict>
		<key>Headers/FBAdSettingsBridge.h</key>
		<dict>
			<key>hash</key>
			<data>
			otXXaNl3ivITTXmQnbVsH+2bqoU=
			</data>
			<key>hash2</key>
			<data>
			UI+J0a95m7Ys7K0cPLtq7WLECIlE530mF+igVHk+JIE=
			</data>
		</dict>
		<key>Headers/FBAdSize.h</key>
		<dict>
			<key>hash</key>
			<data>
			467OPNHvQN+SuTpujVQIu6UHKS8=
			</data>
			<key>hash2</key>
			<data>
			wF90yazUuH2Zcc8Hs/II5eyL1er7xb7pGGtMmHrcKBg=
			</data>
		</dict>
		<key>Headers/FBAdUtilityBridge.h</key>
		<dict>
			<key>hash</key>
			<data>
			gAsta3Ku0CWuWl5UTPHKiH0Htak=
			</data>
			<key>hash2</key>
			<data>
			Ekr7YVyzcAdl6B0H7nbYsV7iUcHpsrzssTApmPvpQsg=
			</data>
		</dict>
		<key>Headers/FBAdView.h</key>
		<dict>
			<key>hash</key>
			<data>
			63WRrf+IX626fzGNbMmHZv24gNA=
			</data>
			<key>hash2</key>
			<data>
			8CDyiOU4r0R1kHWs48WAj7HdOIV2lmk04EUw/XklUdg=
			</data>
		</dict>
		<key>Headers/FBAdViewBridge.h</key>
		<dict>
			<key>hash</key>
			<data>
			OodJ7CV+yZ7t4qMfaw+Kf28t8b8=
			</data>
			<key>hash2</key>
			<data>
			6af6A0ZYL2djXooZTlBkHbvifK1+LfujFaneIDf12qM=
			</data>
		</dict>
		<key>Headers/FBAudienceNetwork.h</key>
		<dict>
			<key>hash</key>
			<data>
			Koz9Fi0nOjJ9WWjoAb1uqzgktNE=
			</data>
			<key>hash2</key>
			<data>
			4JhdtxBNiqqFmzn8zNXa2FymFRWkfEpevEcbvzT80CQ=
			</data>
		</dict>
		<key>Headers/FBAudienceNetworkAds.h</key>
		<dict>
			<key>hash</key>
			<data>
			H/X7/UR12iWRVjaSltqIiArIosM=
			</data>
			<key>hash2</key>
			<data>
			fcBdNhRn8zj/GrDiVlUpRzgjLTDGHz0dEL10YpQdQ70=
			</data>
		</dict>
		<key>Headers/FBDynamicBannerAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			tsiK6PU6SwlcUUO3nvmecWvIq0A=
			</data>
			<key>hash2</key>
			<data>
			HF+SY2KIPEIPMlOcchbxR8Hv/VfkHvqC49yH0SR2Bwc=
			</data>
		</dict>
		<key>Headers/FBInterstitialAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			lIlaP5u77Wek+3RM2x88LAnpVVk=
			</data>
			<key>hash2</key>
			<data>
			c4yQYqi0krH8w3Lgio+oX5bvMdjzOhzIAv1J3CrMfDc=
			</data>
		</dict>
		<key>Headers/FBInterstitialAdBridge.h</key>
		<dict>
			<key>hash</key>
			<data>
			q0sSvvj1o8+x5LmKwxlhIV/565k=
			</data>
			<key>hash2</key>
			<data>
			dj5iE+sKiA2u98F/8Ufr71ZBAG/3usLfdO1HX7TNnq4=
			</data>
		</dict>
		<key>Headers/FBMediaView.h</key>
		<dict>
			<key>hash</key>
			<data>
			XcW9MWOORzas0upnWUPgaLToIaY=
			</data>
			<key>hash2</key>
			<data>
			IdJZ2bSGN2VC8krmDBP0qszbSSkjbQsxqyMQtvl9lCs=
			</data>
		</dict>
		<key>Headers/FBMediaViewVideoRenderer.h</key>
		<dict>
			<key>hash</key>
			<data>
			jD5UU9ICd4La/BLkWy6cDpjBwQw=
			</data>
			<key>hash2</key>
			<data>
			zZVU8tBgqcNFlcqoLwU+8CxPXOMVE6b+2qX6+FcW9qg=
			</data>
		</dict>
		<key>Headers/FBNativeAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			3ja15NIMLK6OWYjdfRKs0kthxi0=
			</data>
			<key>hash2</key>
			<data>
			0Wcadjh4ZkPNNleFsvb5Kqhh6fiKDU+GU4amLf8v/lk=
			</data>
		</dict>
		<key>Headers/FBNativeAdBase.h</key>
		<dict>
			<key>hash</key>
			<data>
			l+lfH5qJMxJy03BevlHJnaMbrhQ=
			</data>
			<key>hash2</key>
			<data>
			rYE2lIFgnjzYrp0OEDhWQCnPiwJkCuAOScq160xRySw=
			</data>
		</dict>
		<key>Headers/FBNativeAdBaseView.h</key>
		<dict>
			<key>hash</key>
			<data>
			rewXycBU2KSWl8gauwuiGTRq6co=
			</data>
			<key>hash2</key>
			<data>
			/7uVyr53xtwPvs70Jm412vHzEoWRwpIv8xUqPi6uREU=
			</data>
		</dict>
		<key>Headers/FBNativeAdCollectionViewAdProvider.h</key>
		<dict>
			<key>hash</key>
			<data>
			/zUpuGlyxJuQVIdk34SYBqg1da8=
			</data>
			<key>hash2</key>
			<data>
			SO+PSlCk/idLK06M76CgFE8qzVRmaNFq+pX6ncbBMgw=
			</data>
		</dict>
		<key>Headers/FBNativeAdCollectionViewCellProvider.h</key>
		<dict>
			<key>hash</key>
			<data>
			5W/OoYUbOOd5XUWJIN2EPFC+5Wg=
			</data>
			<key>hash2</key>
			<data>
			FZqpXowlj0fdzO7lfluA2HCaTWSTU6MyX+OC6yMOQvc=
			</data>
		</dict>
		<key>Headers/FBNativeAdScrollView.h</key>
		<dict>
			<key>hash</key>
			<data>
			AIJ+BPRGadRoFOcJq4byzLCNNVI=
			</data>
			<key>hash2</key>
			<data>
			56CfPO7yPEXW+1j2qNKVpi6fPxjDsdm3pO4R5b1F6Mg=
			</data>
		</dict>
		<key>Headers/FBNativeAdTableViewAdProvider.h</key>
		<dict>
			<key>hash</key>
			<data>
			cTgHcaEBZJiOOHqrNJgFYSOTFxc=
			</data>
			<key>hash2</key>
			<data>
			xb4Qh66pqYjzGIsBIBIDCEZscxJRIr9KN4IL33K0ZzE=
			</data>
		</dict>
		<key>Headers/FBNativeAdTableViewCellProvider.h</key>
		<dict>
			<key>hash</key>
			<data>
			p+bqYNQZS8SWtgZheKS83Hovokg=
			</data>
			<key>hash2</key>
			<data>
			NP81wRmav1YlgTPbO945gezhUmqDDoowhI1udasjQBQ=
			</data>
		</dict>
		<key>Headers/FBNativeAdView.h</key>
		<dict>
			<key>hash</key>
			<data>
			J9FN9bQtZnm2TSMxR6vdypm5OtM=
			</data>
			<key>hash2</key>
			<data>
			Y/qMKttgI1kOxAd2bO2gm+a2r/mXuBj8ZFNhMbz37+w=
			</data>
		</dict>
		<key>Headers/FBNativeAdViewAttributes.h</key>
		<dict>
			<key>hash</key>
			<data>
			+02NIOY5GtfKVtV9WR6ELAKv6gU=
			</data>
			<key>hash2</key>
			<data>
			1qAkpo1/MuwX5jD2vqzoOuNdGRVFHcnZ4OwAHq9HUPY=
			</data>
		</dict>
		<key>Headers/FBNativeAdsManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			SER5cKJ0SHgxJ0VGBKCE9D5pFbQ=
			</data>
			<key>hash2</key>
			<data>
			qn4dEFN6+wCANj1ZLjH64skPimIjztj9n5ClaoUFM+E=
			</data>
		</dict>
		<key>Headers/FBNativeBannerAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			M0ynY/kAU4FSXp0YzQUwPLR8AAE=
			</data>
			<key>hash2</key>
			<data>
			DgxFwbXDiSln1fywfE7xg4b+byh93VYRgcZAxXQ5fZA=
			</data>
		</dict>
		<key>Headers/FBNativeBannerAdView.h</key>
		<dict>
			<key>hash</key>
			<data>
			hPi9YaW20M8SibWRzcHsvxgB7aI=
			</data>
			<key>hash2</key>
			<data>
			e7LXD+yH3fbbFRkgtLJB9i3fytZnLxniZwjKeOdqLSg=
			</data>
		</dict>
		<key>Headers/FBRewardedInterstitialAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			Ja3i8kgcHuIyVokVSFqkrNJJaIc=
			</data>
			<key>hash2</key>
			<data>
			3qP9r9VcXg07rx5l7yxO7/zSjKWYX4xAEx7jFXx/3Ew=
			</data>
		</dict>
		<key>Headers/FBRewardedVideoAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			HTglpeO2IlcK2Sy2WyZsyyu/w+s=
			</data>
			<key>hash2</key>
			<data>
			Y2OUHTfxuwWRs9C41o0hoGV4CBfnkdprZOYY7XS/Jts=
			</data>
		</dict>
		<key>Headers/FBRewardedVideoAdBridge.h</key>
		<dict>
			<key>hash</key>
			<data>
			1bMVuSeL7cq06A9kiGQjLeMvy5s=
			</data>
			<key>hash2</key>
			<data>
			qVNmDTFc5cH9FJUcJ+5UROcaz0L+qAKQS5H+8NjDBks=
			</data>
		</dict>
		<key>Headers/UIView+FBNativeAdViewTag.h</key>
		<dict>
			<key>hash</key>
			<data>
			UIyv4MmxUtXcBhhp1hyYKHadxTA=
			</data>
			<key>hash2</key>
			<data>
			+MnUlXhK83jI9ajQxYZoLkV1ou3hSilb1idDllCkq6E=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			eL2I/YyQvaoNWry0nbEJDR6NAhQ=
			</data>
			<key>hash2</key>
			<data>
			ik1Ragh1i/sv7KVakaF6zQXkvLt6jm/DDar2NKEWScg=
			</data>
		</dict>
		<key>PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			iJf6SpfSfy71MDcGI+K9k1a78DU=
			</data>
			<key>hash2</key>
			<data>
			cSw93xWtIPxdaMlNpIyJu23eaMe/V5QkksYayESwhlc=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
